import { baseApi } from "../../api/baseApi";
import { ICourse } from "@/types/course";

const courseApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCourseById: builder.query<{ data: ICourse }, string>({
      query: (id) => ({
        url: `/courses/${id}`,
        method: "GET",
      }),
      providesTags: (result, _, id) => [
        { type: "course", id },
        "courses",
        // Add lecture-related tags since course data includes lectures
        { type: "lectures", id },
        ...(result?.data?.lectures || []).map((lecture: any) => ({
          type: "lecture" as const,
          id: typeof lecture === 'string' ? lecture : lecture._id
        }))
      ],
    }),
    getPublishedCourses: builder.query<{ data: ICourse[] }, void>({
      query: () => ({
        url: "/courses/published-courses",
        method: "GET",
      }),
      providesTags: (result) => [
        "courses",
        ...(result?.data || []).map((course: ICourse) => ({ type: "course" as const, id: course._id }))
      ],
    }),
    getPopularCourses: builder.query<{ data: ICourse[] }, number>({
      query: (limit = 8) => ({
        url: `/courses/popular-courses?limit=${limit}`,
        method: "GET",
      }),
      providesTags: (result) => [
        "courses",
        ...(result?.data || []).map((course: ICourse) => ({ type: "course" as const, id: course._id }))
      ],
    }),
    searchCourses: builder.query<{ data: ICourse[] }, string>({
      query: (searchTerm) => ({
        url: `/courses/search?searchTerm=${searchTerm}`,
        method: "GET",
      }),
      providesTags: (result) => [
        "courses",
        ...(result?.data || []).map((course: ICourse) => ({ type: "course" as const, id: course._id }))
      ],
    }),
  }),
});

export const {
  useGetCourseByIdQuery,
  useGetPublishedCoursesQuery,
  useGetPopularCoursesQuery,
  useSearchCoursesQuery,
} = courseApi;