import { baseApi } from "@/redux/api/baseApi";
import { TResponseRedux } from "@/types/global";
import { setError, setLoading } from "./courseSlice";
import { toast } from "sonner";

// Enhanced types for better type safety
export interface Lecture {
  _id: string;
  lectureTitle: string;
  instruction?: string;
  videoUrl?: string;
  pdfUrl?: string;
  isPreviewFree?: boolean;
  courseId: string;
  order?: number;
  duration?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface Course {
  _id: string;
  title: string;
  subtitle?: string;
  description?: string;
  category: string;
  subcategory?: string;
  courseLevel: "Beginner" | "Medium" | "Advance";
  coursePrice: number;
  courseThumbnail: string;
  enrolledStudents: string[];
  lectures: string[] | Lecture[]; // Support both lecture IDs and populated lecture objects
  creator: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
  totalLectures?: number;
  totalDuration?: number;
  averageRating?: number;
  totalReviews?: number;
}

export interface CreateCourseRequest {
  title: string;
  subtitle?: string;
  description?: string;
  categoryId?: string;
  subcategoryId?: string;
  courseLevel: "Beginner" | "Medium" | "Advance";
  coursePrice: number;
}

export interface UpdateCourseRequest extends Partial<CreateCourseRequest> {
  isPublished?: boolean;
}

export interface GetCoursesParams {
  teacherId?: string;
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const courseApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    createCourse: builder.mutation<
      { data: Course },
      { id: string; data: CreateCourseRequest | FormData; file?: File }
    >({
      query: (args) => {
        // If data is already FormData, use it directly
        if (args.data instanceof FormData) {
          // Add thumbnail if provided and not already in FormData
          if (args.file && !args.data.has("file")) {
            args.data.append("file", args.file);
          }
          return {
            url: `/courses/create-course/${args.id}`,
            method: "POST",
            body: args.data,
          };
        }

        // Otherwise, create FormData from object
        const formData = new FormData();

        // Add course data to FormData
        Object.keys(args.data).forEach((key) => {
          const value = args.data[key as keyof CreateCourseRequest];
          if (value !== undefined) {
            formData.append(key, value.toString());
          }
        });

        // Add thumbnail if provided
        if (args.file) {
          formData.append("file", args.file);
        }

        return {
          url: `/courses/create-course/${args.id}`,
          method: "POST",
          body: formData,
        };
      },
      transformResponse: (response: TResponseRedux<Course>) => ({
        data: response.data,
      }),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        dispatch(setLoading(true));
        try {
          await queryFulfilled;
          toast.success("Course created successfully!");

          // Force refetch of published courses to ensure immediate visibility
          dispatch(courseApi.util.invalidateTags([
            "courses",
            { type: "courses", id: "published" }
          ]));
        } catch (error: any) {
          const errorMessage = error?.error?.data?.message || "Failed to create course";
          dispatch(setError(errorMessage));
          toast.error(errorMessage);
        } finally {
          dispatch(setLoading(false));
        }
      },
      invalidatesTags: (_, __, args) => [
        // Invalidate all course-related caches
        "courses", // General courses tag
        { type: "courses", id: "LIST" }, // Course list
        { type: "courses", id: "published" }, // Published courses
        { type: "courses", id: `creator-${args.id}` }, // Creator-specific courses
        "course" // Individual course tag
      ],
    }),

    getCourses: builder.query<
      { data: Course[]; meta?: any },
      GetCoursesParams
    >({
      query: (params) => {
        const searchParams = new URLSearchParams();

        if (params.teacherId) searchParams.append('teacherId', params.teacherId);
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.search) searchParams.append('search', params.search);
        if (params.category) searchParams.append('category', params.category);
        if (params.status) searchParams.append('status', params.status);
        if (params.sortBy) searchParams.append('sortBy', params.sortBy);
        if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

        return {
          url: `/courses/creator/${params.teacherId}?${searchParams.toString()}`,
          method: "GET",
        };
      },
      providesTags: (result, _, args) => [
        { type: "courses", id: `creator-${args.teacherId}` },
        { type: "courses", id: "LIST" },
        "courses",
        ...(result?.data || []).map((course) => ({ type: "course" as const, id: course._id }))
      ],
      transformResponse: (response: TResponseRedux<Course[]>) => ({
        data: response.data || [],
        meta: response.meta
      }),
      // Reduced caching for real-time updates
      keepUnusedDataFor: 60, // 1 minute for faster updates
    }),

    getCreatorCourse: builder.query({
      query: (args) => ({
        url: `/courses/creator/${args.id}`,
        method: "GET",
      }),
      providesTags: (result, _, args) => [
        { type: "courses", id: `creator-${args.id}` },
        "courses",
        ...(result?.data || []).map((course: any) => ({ type: "course" as const, id: course._id }))
      ],
      transformResponse: (response: TResponseRedux<any>) => ({
        data: response.data,
      }),
    }),
    getPublishedCourses: builder.query({
      query: () => ({
        url: "/courses/published-courses",
        method: "GET",
      }),
      providesTags: (result) => [
        { type: "courses", id: "published" },
        "courses",
        ...(result?.data || []).map((course: any) => ({ type: "course" as const, id: course._id }))
      ],
      transformResponse: (response: TResponseRedux<any>) => {
        // Handle potential errors or empty responses
        if (!response || !response.data) {
          console.error("Invalid response from published courses API:", response);
          return { data: [], meta: {} };
        }

        // The backend returns the courses directly in the data field
        return {
          data: response.data,
          meta: response.meta,
        };
      },
      // Add error handling
      onQueryStarted: async (_, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {
          console.error("Error fetching published courses:", error);
          // Don't dispatch logout for public endpoint errors
        }
      },
    }),
    getPopularCourses: builder.query({
      query: (limit?: number) => ({
        url: `/courses/popular-courses${limit ? `?limit=${limit}` : ''}`,
        method: "GET",
      }),
      providesTags: (result) => [
        { type: "courses", id: "popular" },
        "courses",
        ...(result?.data || []).map((course: any) => ({ type: "course" as const, id: course._id }))
      ],
      transformResponse: (response: TResponseRedux<any>) => {
        // Handle potential errors or empty responses
        if (!response || !response.data) {
          console.error("Invalid response from popular courses API:", response);
          return { data: [] };
        }

        return {
          data: response.data,
        };
      },
      // Add error handling
      onQueryStarted: async (_, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {
          console.error("Error fetching popular courses:", error);
          // Don't dispatch logout for public endpoint errors
        }
      },
    }),
    getCourseById: builder.query({
      query: (id) => {
        console.log("getCourseById query function called with ID:", id);
        if (!id) {
          console.error("getCourseById called with invalid ID:", id);
          throw new Error("Invalid course ID");
        }
        return {
          url: `/courses/${id}`,
          method: "GET",
        };
      },
      providesTags: (result, _, id) => [
        { type: "course", id },
        "courses",
        // Add lecture-related tags since course data includes lectures
        { type: "lectures", id },
        ...(result?.data?.lectures || []).map((lecture: any) => ({
          type: "lecture" as const,
          id: typeof lecture === 'string' ? lecture : lecture._id
        }))
      ],
      transformResponse: (response: TResponseRedux<any>) => {
        console.log("getCourseById response:", response);
        if (!response.data) {
          console.error("No data in response:", response);
          throw new Error("No course data found");
        }
        return {
          data: response.data,
        };
      },
      onQueryStarted: async (id, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error: any) {
          // Check if it's an access error (403)
          if (error?.error?.status === 403) {
            dispatch(setError("You don't have permission to access this course"));
          } else if (error?.error?.status === 404) {
            dispatch(setError("Course not found"));
          } else if (error?.error?.status === 401) {
            dispatch(setError("You need to be logged in to access this course"));
          } else {
            dispatch(setError("Error fetching course"));
          }
        }
      },
    }),
    editCourse: builder.mutation({
      query: (args) => {
        // For FormData, we need to let the browser set the Content-Type
        // to include the boundary string
        const formData = new FormData();

        // Add course data to FormData
        Object.keys(args.data).forEach((key) => {
          formData.append(key, args.data[key]);
        });

        // Add thumbnail if provided
        if (args.file) {
          formData.append("file", args.file);
        }

        return {
          url: `/courses/edit-course/${args.id}`,
          method: "PATCH",
          body: formData,
          formData: true, // This signals to use FormData
        };
      },
      transformResponse: (response: TResponseRedux<any>) => ({
        data: response.data,
      }),
      onQueryStarted: async (args, { dispatch, queryFulfilled, getState }) => {
        dispatch(setLoading(true));

        // Optimistic update for course details
        const patchResult = dispatch(
          courseApi.util.updateQueryData('getCourseById', args.id, (draft) => {
            if (draft?.data) {
              Object.assign(draft.data, args.data);
            }
          })
        );

        // Optimistic update for creator courses list
        const creatorPatchResult = dispatch(
          courseApi.util.updateQueryData('getCreatorCourse', { id: args.creatorId }, (draft) => {
            if (draft?.data) {
              const courseIndex = draft.data.findIndex((course: any) => course._id === args.id);
              if (courseIndex !== -1) {
                Object.assign(draft.data[courseIndex], args.data);
              }
            }
          })
        );

        try {
          const result = await queryFulfilled;

          // Update with real data from server
          dispatch(
            courseApi.util.updateQueryData('getCourseById', args.id, (draft) => {
              if (draft?.data) {
                Object.assign(draft.data, result.data);
              }
            })
          );

          // Force immediate cache invalidation for real-time sync
          setTimeout(() => {
            dispatch(baseApi.util.invalidateTags([
              'courses',
              'course',
              { type: 'courses', id: 'LIST' },
              { type: 'courses', id: 'published' },
              { type: 'courses', id: `creator-${args.creatorId}` },
              { type: 'course', id: args.id }
            ]));
          }, 0);

        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          creatorPatchResult.undo();
          dispatch(setError("Error editing course"));
        } finally {
          dispatch(setLoading(false));
        }
      },
      invalidatesTags: (result, error, args) => [
        // Invalidate all course-related caches
        "courses", // General courses tag
        { type: "course", id: args.id }, // Specific course
        { type: "courses", id: "LIST" }, // Course list
        { type: "courses", id: "published" }, // Published courses
        { type: "courses", id: `creator-${args.creatorId}` }, // Creator-specific courses
      ],
    }),
    deleteCourse: builder.mutation({
      query: (id) => ({
        url: `/courses/delete-course/${id}`,
        method: "DELETE",
      }),
      transformResponse: (response: TResponseRedux<any>) => ({
        data: response.data,
      }),
      onQueryStarted: async (args, { dispatch, queryFulfilled }) => {
        dispatch(setLoading(true));
        try {
          await queryFulfilled;
        } catch (error) {
          dispatch(setError("Error deleting course"));
        } finally {
          dispatch(setLoading(false));
        }
      },
      invalidatesTags: [
        // Invalidate all course-related caches
        "courses", // General courses tag
        { type: "courses", id: "LIST" }, // Course list
        { type: "courses", id: "published" }, // Published courses
        "course" // Individual course tag
      ],
    }),
  }),
});

export const {
  useCreateCourseMutation,
  useGetCoursesQuery,
  useGetCreatorCourseQuery,
  useGetPublishedCoursesQuery,
  useGetPopularCoursesQuery,
  useGetCourseByIdQuery,
  useEditCourseMutation,
  useDeleteCourseMutation,
} = courseApi;
