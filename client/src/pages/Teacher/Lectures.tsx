import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  RefreshCw,
  Eye,
  Edit,
  Clock,
  Video,
  Grid,
  List,
  MoreHorizontal,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { useGetMeQuery } from '@/redux/features/auth/authApi';
import { useGetCreatorCourseQuery } from '@/redux/features/course/courseApi';
import { baseApi } from '@/redux/api/baseApi';
import { useAppDispatch } from '@/redux/hooks';
import { ICourse } from '@/types/course';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/utils/formatDuration';
import { toast } from 'sonner';

interface LectureWithCourse {
  _id: string;
  lectureTitle: string;
  instruction?: string;
  videoUrl?: string;
  pdfUrl?: string;
  isPreviewFree?: boolean;
  duration?: number;
  order?: number;
  courseId: string;
  createdAt: string;
  updatedAt: string;
  course?: ICourse;
}

interface FilterOptions {
  course: string;
  status: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const Lectures: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [filters, setFilters] = useState<FilterOptions>({
    course: 'all',
    status: 'all',
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });

  // Get current user
  const { data: userData, isLoading: isUserLoading } = useGetMeQuery(undefined);
  const teacherId = userData?.data?._id;

  // Get teacher's courses
  const {
    data: coursesData,
    isLoading: isCoursesLoading,
    refetch: refetchCourses,
  } = useGetCreatorCourseQuery(
    { id: teacherId! },
    { skip: !teacherId }
  );

  const courses = useMemo(() => {
    return coursesData?.data || [];
  }, [coursesData?.data]);

  // Get lectures from courses data (if populated)
  const allLectures = useMemo(() => {
    const combinedLectures: LectureWithCourse[] = [];
    
    // Check if courses have populated lectures
    courses.forEach((course: ICourse) => {
      if (course.lectures && Array.isArray(course.lectures)) {
        const courseLectures = course.lectures.map((lecture: any) => ({
          ...lecture,
          courseId: course._id,
          course: course,
        }));
        combinedLectures.push(...courseLectures);
      }
    });

    return combinedLectures;
  }, [courses]);

  // Filter and search lectures
  const filteredLectures = useMemo(() => {
    let filtered = [...allLectures];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(lecture =>
        lecture.lectureTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lecture.instruction?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lecture.course?.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Course filter
    if (filters.course !== 'all') {
      filtered = filtered.filter(lecture => lecture.courseId === filters.course);
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (filters.sortBy) {
        case 'title':
          aValue = a.lectureTitle;
          bValue = b.lectureTitle;
          break;
        case 'course':
          aValue = a.course?.title || '';
          bValue = b.course?.title || '';
          break;
        case 'duration':
          aValue = a.duration || 0;
          bValue = b.duration || 0;
          break;
        case 'order':
          aValue = a.order || 0;
          bValue = b.order || 0;
          break;
        default:
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [allLectures, searchQuery, filters]);

  // Calculate stats
  const stats = useMemo(() => {
    const totalLectures = allLectures.length;
    const totalDuration = allLectures.reduce((sum, lecture) => sum + (lecture.duration || 0), 0);
    const publishedLectures = allLectures.filter(lecture => lecture.videoUrl).length;
    const draftLectures = totalLectures - publishedLectures;

    return {
      totalLectures,
      totalDuration,
      publishedLectures,
      draftLectures,
    };
  }, [allLectures]);

  const handleRefresh = useCallback(async () => {
    try {
      // Force cache invalidation
      dispatch(baseApi.util.invalidateTags([
        'courses',
        'course',
        'lectures',
        'lecture',
        { type: 'courses', id: 'LIST' },
        { type: 'courses', id: `creator-${teacherId}` },
        { type: 'lecture', id: 'LIST' }
      ]));
      
      await refetchCourses();
      toast.success('Lectures refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh lectures');
    }
  }, [dispatch, refetchCourses, teacherId]);

  const handleEditLecture = useCallback((lecture: LectureWithCourse) => {
    navigate(`/teacher/courses/${lecture.courseId}/lecture/edit/${lecture._id}`);
  }, [navigate]);

  const handleViewCourse = useCallback((lecture: LectureWithCourse) => {
    navigate(`/teacher/courses/${lecture.courseId}/details`);
  }, [navigate]);

  // Force refresh when component mounts (e.g., returning from edit page)
  useEffect(() => {
    if (teacherId) {
      // Invalidate cache to ensure fresh data
      dispatch(baseApi.util.invalidateTags([
        'courses',
        'course',
        'lectures',
        'lecture',
        { type: 'courses', id: `creator-${teacherId}` },
        { type: 'lecture', id: 'LIST' }
      ]));
    }
  }, [dispatch, teacherId]);

  const isLoading = isUserLoading || isCoursesLoading;

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto space-y-8 p-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-24" />
          ))}
        </div>
        <Skeleton className="h-96" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-8 p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
            Lectures Management
          </h1>
          <p className="text-gray-600 text-lg">
            Manage and organize all your course lectures in one place.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button
            onClick={() => navigate('/teacher/courses')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Course
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Video className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Lectures</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalLectures}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Duration</p>
                <p className="text-2xl font-bold text-gray-900">{formatDuration(stats.totalDuration)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Eye className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">{stats.publishedLectures}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Edit className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-gray-900">{stats.draftLectures}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search lectures..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={filters.course} onValueChange={(value) => setFilters(prev => ({ ...prev, course: value }))}>
              <SelectTrigger className="w-full lg:w-48">
                <SelectValue placeholder="Filter by course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course: ICourse) => (
                  <SelectItem key={course._id} value={course._id}>
                    {course.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.sortBy} onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value }))}>
              <SelectTrigger className="w-full lg:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updatedAt">Last Updated</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="course">Course</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="order">Order</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('table')}
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lectures Content */}
      {filteredLectures.length === 0 ? (
        <div className="text-center py-12">
          <Video className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No lectures found</h3>
          <p className="text-gray-500 mb-6">
            {searchQuery || filters.course !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start by creating your first course and adding lectures'}
          </p>
          <Button
            onClick={() => navigate('/teacher/courses')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Course
          </Button>
        </div>
      ) : viewMode === 'table' ? (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Lecture</TableHead>
                  <TableHead>Course</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLectures.map((lecture) => (
                  <TableRow key={lecture._id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Video className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{lecture.lectureTitle}</p>
                          {lecture.instruction && (
                            <p className="text-sm text-gray-500 truncate max-w-xs">
                              {lecture.instruction}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-900">{lecture.course?.title}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">
                        {lecture.duration ? formatDuration(lecture.duration) : 'N/A'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge variant={lecture.videoUrl ? 'default' : 'secondary'}>
                        {lecture.videoUrl ? 'Published' : 'Draft'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">
                        {new Date(lecture.updatedAt).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditLecture(lecture)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Lecture
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewCourse(lecture)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Course
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLectures.map((lecture) => (
            <Card key={lecture._id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Video className="w-6 h-6 text-blue-600" />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditLecture(lecture)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Lecture
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleViewCourse(lecture)}>
                        <Eye className="w-4 h-4 mr-2" />
                        View Course
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {lecture.lectureTitle}
                </h3>

                <p className="text-sm text-gray-600 mb-3">
                  {lecture.course?.title}
                </p>

                {lecture.instruction && (
                  <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                    {lecture.instruction}
                  </p>
                )}

                <div className="flex items-center justify-between">
                  <Badge variant={lecture.videoUrl ? 'default' : 'secondary'}>
                    {lecture.videoUrl ? 'Published' : 'Draft'}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {lecture.duration ? formatDuration(lecture.duration) : 'N/A'}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default Lectures;
