/**
 * Test script to verify Redux cache invalidation is working properly
 * This can be run in the browser console or as a standalone test
 */

import { store } from '../redux/store';
import { baseApi } from '../redux/api/baseApi';

export const testCacheInvalidation = async () => {
  console.log('🧪 Testing Redux Cache Invalidation...');
  
  // Get current state
  const initialState = store.getState();
  console.log('📊 Initial API state:', initialState.baseApi);
  
  // Test 1: Check if cache tags are properly configured
  console.log('\n🏷️ Testing cache tag configuration...');
  
  // Get all registered endpoints
  const endpoints = baseApi.endpoints;
  console.log('📋 Available endpoints:', Object.keys(endpoints));
  
  // Check specific endpoints for proper cache tags
  const courseEndpoints = Object.keys(endpoints).filter(key => key.includes('Course'));
  const lectureEndpoints = Object.keys(endpoints).filter(key => key.includes('Lecture'));
  
  console.log('🎓 Course endpoints:', courseEndpoints);
  console.log('📚 Lecture endpoints:', lectureEndpoints);
  
  // Test 2: Simulate cache invalidation
  console.log('\n🔄 Testing cache invalidation...');
  
  try {
    // Invalidate specific tags
    store.dispatch(baseApi.util.invalidateTags([
      'courses',
      'lectures',
      { type: 'course', id: 'test-course-id' },
      { type: 'lecture', id: 'test-lecture-id' }
    ]));
    
    console.log('✅ Cache invalidation dispatched successfully');
    
    // Check state after invalidation
    const stateAfterInvalidation = store.getState();
    console.log('📊 State after invalidation:', stateAfterInvalidation.baseApi);
    
  } catch (error) {
    console.error('❌ Cache invalidation failed:', error);
  }
  
  // Test 3: Check tag relationships
  console.log('\n🔗 Testing tag relationships...');
  
  const tagTypes = [
    'courses',
    'course', 
    'lectures',
    'lecture'
  ];
  
  tagTypes.forEach(tagType => {
    console.log(`🏷️ Tag type "${tagType}" is configured in baseApi`);
  });
  
  console.log('\n✅ Cache invalidation test completed!');
  
  return {
    success: true,
    message: 'Cache invalidation test completed successfully',
    endpoints: {
      course: courseEndpoints,
      lecture: lectureEndpoints
    }
  };
};

// Helper function to monitor cache changes
export const monitorCacheChanges = () => {
  let previousState = store.getState().baseApi;
  
  const unsubscribe = store.subscribe(() => {
    const currentState = store.getState().baseApi;
    
    if (currentState !== previousState) {
      console.log('🔄 Cache state changed:', {
        previous: Object.keys(previousState.queries || {}),
        current: Object.keys(currentState.queries || {}),
        timestamp: new Date().toISOString()
      });
      previousState = currentState;
    }
  });
  
  console.log('👀 Cache monitoring started. Call the returned function to stop monitoring.');
  return unsubscribe;
};

// Helper function to check specific query cache
export const checkQueryCache = (endpointName: string, args?: any) => {
  const state = store.getState();
  const queries = state.baseApi.queries;
  
  console.log(`🔍 Checking cache for ${endpointName}:`, {
    endpointName,
    args,
    cachedQueries: Object.keys(queries || {}),
    relevantQueries: Object.keys(queries || {}).filter(key => key.includes(endpointName))
  });
  
  return Object.keys(queries || {}).filter(key => key.includes(endpointName));
};

// Helper function to test lecture update cache invalidation
export const testLectureUpdateCacheInvalidation = async (courseId: string, lectureId: string) => {
  console.log('🧪 Testing lecture update cache invalidation...');

  const initialState = store.getState();
  console.log('📊 Initial cache state:', Object.keys(initialState.baseApi.queries || {}));

  // Monitor cache changes during update
  let cacheChanges: string[] = [];
  const unsubscribe = store.subscribe(() => {
    const currentState = store.getState();
    const currentQueries = Object.keys(currentState.baseApi.queries || {});
    cacheChanges.push(`Cache updated: ${currentQueries.length} queries at ${new Date().toISOString()}`);
  });

  try {
    // Simulate cache invalidation that would happen during lecture update
    store.dispatch(baseApi.util.invalidateTags([
      { type: 'lecture', id: lectureId },
      { type: 'lectures', id: courseId },
      { type: 'course', id: courseId },
      'courses',
      'lectures'
    ]));

    console.log('✅ Cache invalidation completed');
    console.log('📈 Cache changes:', cacheChanges);

    return {
      success: true,
      cacheChanges,
      message: 'Lecture update cache invalidation test completed'
    };
  } catch (error) {
    console.error('❌ Cache invalidation test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      cacheChanges
    };
  } finally {
    unsubscribe();
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testCacheInvalidation = testCacheInvalidation;
  (window as any).monitorCacheChanges = monitorCacheChanges;
  (window as any).checkQueryCache = checkQueryCache;
  (window as any).testLectureUpdateCacheInvalidation = testLectureUpdateCacheInvalidation;
}
